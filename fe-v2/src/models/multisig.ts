import { useState, useCallback, useEffect } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import Api from '@/services/api';
import { MultisigInfo } from '@/services/types';

// 全局变量，防止重复请求
let isRequesting = false;
let requestPromise: Promise<MultisigInfo> | null = null;

export default function useMultisigModel() {
  const { publicKey } = useWallet();
  const [multisigs, setMultisigs] = useState<MultisigInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [userBalance, setUserBalance] = useState<number>(0);
  const [solPrice, setSolPrice] = useState<number>(100);
  const [totalVaultValue, setTotalVaultValue] = useState<number>(0);

  const refreshMultisigs = useCallback(async (forceRefresh = false) => {
    // 如果正在请求中且不是强制刷新，则等待当前请求完成
    if (isRequesting && !forceRefresh && requestPromise) {
      try {
        const result = await requestPromise;
        setMultisigs([result]);
        setSolPrice(result.solPrice);
        setTotalVaultValue(result.vault.totalValue);
        setError(null);
      } catch (err: any) {
        setError(err.message || '获取多签信息失败');
      }
      return;
    }

    // 如果已有数据且不是强制刷新，直接返回
    if (!forceRefresh && multisigs.length > 0) {
      return;
    }

    try {
      setLoading(true);
      setError(null);
      isRequesting = true;

      // 创建请求 Promise
      requestPromise = Api.getMultisigs();
      const multisigResult = await requestPromise;

      // 将单个多签信息包装成数组格式，保持兼容性
      setMultisigs([multisigResult]);
      setSolPrice(multisigResult.solPrice);
      setTotalVaultValue(multisigResult.vault.totalValue);

    } catch (err: any) {
      console.error('获取多签信息失败:', err);
      setError(err.message || '获取多签信息失败');
      setMultisigs([]);
      setTotalVaultValue(0);
    } finally {
      setLoading(false);
      isRequesting = false;
      requestPromise = null;
    }
  }, [multisigs.length]);

  // 获取用户钱包余额
  const refreshUserBalance = useCallback(async () => {
    if (!publicKey) {
      setUserBalance(0);
      return;
    }

    try {
      const result = await Api.getUserBalance(publicKey.toBase58());
      setUserBalance(result.balance / 1e9); // 转换为 SOL
    } catch (err: any) {
      console.error('获取用户余额失败:', err);
      setUserBalance(0);
    }
  }, [publicKey]);

  // 当钱包连接状态变化时，刷新用户余额
  useEffect(() => {
    refreshUserBalance();
  }, [refreshUserBalance]);

  // 当前多签账户（通常是第一个，因为后端配置的是固定的）
  const currentMultisig = multisigs.length > 0 ? multisigs[0] : null;

  return {
    multisigs,
    currentMultisig,
    userBalance, // Phantom 钱包的 SOL 余额
    totalVaultValue, // 多签金库总价值（美元）
    solPrice, // SOL 价格
    loading,
    error,
    refreshMultisigs,
    refreshUserBalance,
    // 为了向后兼容，保留 totalBalance 但指向用户余额
    totalBalance: userBalance,
  };
}
