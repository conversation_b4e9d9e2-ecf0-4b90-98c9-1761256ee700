import { MultisigInfo, Transaction, Recipient, TransactionListResponse } from './types';

const API_BASE_URL = 'http://localhost:3001';

class Api {
  private static async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);
      }
      const data = await response.json();
      return data;
    } catch (error) {
      console.error(`API request failed: ${endpoint}`, error);
      throw error;
    }
  }

  static async getBlockhash() {
    return this.request<{ blockhash: string }>('/api/blockhash');
  }

  // ==================== 账户信息接口 ====================

  static async getMultisigs() {
    return this.request<MultisigInfo>('/api/multisigs');
  }





  static async getRecipients() {
    return this.request<any>('/api/tos');
  }

  // 获取用户钱包余额
  static async getUserBalance(publicKey: string) {
    return this.request<{ balance: number }>('/api/balance', {
      method: 'POST',
      body: JSON.stringify({ publicKey }),
    });
  }

  // ==================== 转账接口 ====================

  static async createTransfer(params: any): Promise<any> {
    return this.request('/api/transfer', {
      method: 'POST',
      body: JSON.stringify(params),
    });
  }

  // ==================== 交易管理接口 ====================

  static async getTransactions(page: number = 1, pageSize: number = 5) {
    return this.request<TransactionListResponse>('/api/transactions', {
      method: 'POST',
      body: JSON.stringify({ page, pageSize }),
    });
  }

  static async buildExecuteInstruction(params: {
    multisigAddress: string;
    transactionIndex: number;
    executorPublicKey: string;
  }) {
    return this.request<{
      instruction: {
        keys: Array<{
          pubkey: string;
          isSigner: boolean;
          isWritable: boolean;
        }>;
        programId: string;
        data: number[];
      };
      lookupTableAccounts: any[];
    }>('/api/transactions/build-execute', {
      method: 'POST',
      body: JSON.stringify(params),
    });
  }

  static async voteTransaction(params: {
    multisigAddress: string;
    transactionIndex: number;
    userPublicKey: string;
    signedTransaction: string;
    vote: 'approve' | 'reject';
  }) {
    return this.request<{ signature: string }>('/api/transactions/vote', {
      method: 'POST',
      body: JSON.stringify(params),
    });
  }

  static async executeTransaction(params: {
    multisigAddress: string;
    transactionIndex: number;
    userPublicKey: string;
    signedTransaction: string;
  }) {
    return this.request<{ signature: string }>('/api/transactions/execute', {
      method: 'POST',
      body: JSON.stringify(params),
    });
  }

  static async cancelTransaction(params: {
    multisigAddress: string;
    transactionIndex: number;
    userPublicKey: string;
    signedTransaction: string;
  }) {
    return this.request<{ signature: string }>('/api/transactions/cancel', {
      method: 'POST',
      body: JSON.stringify(params),
    });
  }
}

export default Api;