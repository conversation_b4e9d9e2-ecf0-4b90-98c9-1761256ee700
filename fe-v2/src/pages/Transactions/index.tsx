import React, { useState, useCallback, useEffect } from 'react'
import { Table, Button, Tag, message, Space, Pagination, Select, Card } from 'antd'
import { useWallet } from '@solana/wallet-adapter-react'
import { PublicKey, TransactionMessage, VersionedTransaction } from '@solana/web3.js'
import * as multisig from '@sqds/multisig'
import { CheckCircleOutlined, CloseCircleOutlined, ClockCircleOutlined, PlayCircleOutlined } from '@ant-design/icons'
import { useModel } from '@umijs/max'
import Api from '@/services/api'
import type { ColumnsType } from 'antd/es/table'

const TransactionsPage: React.FC = () => {
  const [messageApi, contextHolder] = message.useMessage()
  const { publicKey, signTransaction } = useWallet()
  const { currentMultisig, refreshMultisigs } = useModel('multisig')
  const [transactions, setTransactions] = useState<[]>([])
  const [loading, setLoading] = useState(false)
  const [actionLoading, setActionLoading] = useState<string>('')

  // 分页状态
  const [pagination, setPagination] = useState({
    page: 1,
    pageSize: 5,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false
  })


  // 默认配置
  const programId = new PublicKey("SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf")

  const handleSuccess = (text: string) => {
    messageApi.success(text)
  }

  const handleError = (text: string) => {
    messageApi.error(text)
  }

  const loadTransactions = useCallback(async (page?: number, pageSize?: number) => {
    if (!currentMultisig || !publicKey) return

    setLoading(true)

    try {
      const currentPage = page || pagination.page || 1
      const currentPageSize = pageSize || pagination.pageSize || 5

      const result = await Api.getTransactions(currentPage, currentPageSize)

      setTransactions(result.transactions)
      setPagination(result.pagination)

    } catch (error: any) {
      console.error('加载交易失败:', error)
      handleError(error.message || '加载交易失败')
      setTransactions([])
      // 重置分页状态为默认值
      setPagination({
        page: 1,
        pageSize: 5,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false
      })
    } finally {
      setLoading(false)
    }
  }, [currentMultisig, publicKey])

  useEffect(() => {
    loadTransactions()
  }, [loadTransactions])

  // Transactions 页面加载多签数据
  useEffect(() => {
    refreshMultisigs()
  }, [])

  // 分页处理函数
  const handlePageChange = useCallback((page: number, pageSize?: number) => {
    loadTransactions(page, pageSize)
  }, [loadTransactions])

  const handlePageSizeChange = useCallback((current: number, size: number) => {
    loadTransactions(1, size) // 改变页面大小时回到第一页
  }, [loadTransactions])

  const handleVote = useCallback(async (transactionIndex: number, vote: 'approve' | 'reject') => {
    if (!signTransaction || !publicKey || !currentMultisig) return

    // 检查用户是否有 Voter 权限
    const currentUser = currentMultisig.members.find(member => member.address === publicKey.toBase58())
    if (!currentUser) {
      handleError('当前钱包不是多签成员')
      return
    }
    if (!currentUser.permissions.includes('Voter')) {
      handleError('您没有投票权限（需要 Voter 权限）')
      return
    }

    try {
      setActionLoading(`${vote}-${transactionIndex}`)

      // 构建投票指令
      const multisigPda = new PublicKey(currentMultisig.multisigAccount)
      const instruction = vote === 'approve'
        ? multisig.instructions.proposalApprove({
            multisigPda,
            transactionIndex: BigInt(transactionIndex),
            member: publicKey,
            programId,
          })
        : multisig.instructions.proposalReject({
            multisigPda,
            transactionIndex: BigInt(transactionIndex),
            member: publicKey,
            programId,
          })

      // 获取最新区块哈希
      const { blockhash } = await Api.getBlockhash()

      // 创建交易消息
      const message = new TransactionMessage({
        payerKey: publicKey,
        recentBlockhash: blockhash,
        instructions: [instruction],
      })

      // 创建交易
      const transaction = new VersionedTransaction(message.compileToV0Message())
      const signedTransaction = await signTransaction(transaction)

      // 提交投票
      const result = await Api.voteTransaction({
        multisigAddress: currentMultisig.multisigAccount,
        transactionIndex,
        userPublicKey: publicKey.toBase58(),
        signedTransaction: Buffer.from(signedTransaction.serialize()).toString('base64'),
        vote,
      })

      handleSuccess(result.signature)
      await loadTransactions()

    } catch (error: any) {
      console.error('投票失败:', error)
      handleError(error.message || '投票失败')
    } finally {
      setActionLoading('')
    }
  }, [signTransaction, publicKey, currentMultisig, loadTransactions])

  const handleExecute = useCallback(async (transactionIndex: number) => {
    if (!signTransaction || !publicKey || !currentMultisig) return

    // 检查用户是否有 Executor 权限
    const currentUser = currentMultisig.members.find(member => member.address === publicKey.toBase58())
    if (!currentUser) {
      handleError('当前钱包不是多签成员')
      return
    }
    if (!currentUser.permissions.includes('Executor')) {
      handleError('您没有执行权限（需要 Executor 权限）')
      return
    }

    try {
      setActionLoading(`execute-${transactionIndex}`)

      // 获取执行指令
      const result = await Api.buildExecuteInstruction({
        multisigAddress: currentMultisig.multisigAccount,
        transactionIndex,
        executorPublicKey: publicKey.toBase58(),
      })

      // 获取最新区块哈希
      const { blockhash } = await Api.getBlockhash()

      // 转换指令格式
      const instruction = {
        keys: result.instruction.keys.map(key => ({
          pubkey: new PublicKey(key.pubkey),
          isSigner: key.isSigner,
          isWritable: key.isWritable,
        })),
        programId: new PublicKey(result.instruction.programId),
        data: Buffer.from(result.instruction.data),
      }

      // 创建交易消息
      const message = new TransactionMessage({
        payerKey: publicKey,
        recentBlockhash: blockhash,
        instructions: [instruction],
      })

      // 创建交易
      const transaction = new VersionedTransaction(message.compileToV0Message())
      const signedTransaction = await signTransaction(transaction)

      // 执行交易
      const executeResult = await Api.executeTransaction({
        multisigAddress: currentMultisig.multisigAccount,
        transactionIndex,
        userPublicKey: publicKey.toBase58(),
        signedTransaction: Buffer.from(signedTransaction.serialize()).toString('base64'),
      })

      handleSuccess(executeResult.signature)
      await loadTransactions()

    } catch (error: any) {
      console.error('执行交易失败:', error)
      handleError(error.message || '执行交易失败')
    } finally {
      setActionLoading('')
    }
  }, [signTransaction, publicKey, currentMultisig, loadTransactions])

  const handleCancel = useCallback(async (transactionIndex: number) => {
    if (!signTransaction || !publicKey || !currentMultisig) return

    // 检查用户是否有权限取消交易
    const currentUser = currentMultisig.members.find(member => member.key === publicKey.toBase58())
    if (!currentUser) {
      handleError('当前钱包不是多签成员')
      return
    }

    try {
      setActionLoading(`cancel-${transactionIndex}`)

      // 构建取消指令
      const multisigPda = new PublicKey(currentMultisig.multisigAccount)
      const instruction = multisig.instructions.proposalCancel({
        multisigPda,
        transactionIndex: BigInt(transactionIndex),
        member: publicKey,
        programId,
      })

      // 获取最新区块哈希
      const { blockhash } = await Api.getBlockhash()

      // 创建交易消息
      const message = new TransactionMessage({
        payerKey: publicKey,
        recentBlockhash: blockhash,
        instructions: [instruction],
      })

      // 创建交易
      const transaction = new VersionedTransaction(message.compileToV0Message())
      const signedTransaction = await signTransaction(transaction)

      // 提交取消
      const result = await Api.cancelTransaction({
        multisigAddress: currentMultisig.multisigAccount,
        transactionIndex,
        userPublicKey: publicKey.toBase58(),
        signedTransaction: Buffer.from(signedTransaction.serialize()).toString('base64'),
      })

      handleSuccess('交易已取消')
      await loadTransactions()

    } catch (error: any) {
      console.error('取消交易失败:', error)
      handleError(error.message || '取消交易失败')
    } finally {
      setActionLoading('')
    }
  }, [signTransaction, publicKey, currentMultisig, loadTransactions])

  // 格式化时间显示
  const formatTime = (timeString: string | null) => {
    if (!timeString) return '-'
    try {
      const date = new Date(timeString)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
      })
    } catch {
      return '-'
    }
  }

  const formatAddress = (address: string) => {
    return `${address.slice(0, 4)}...${address.slice(-4)}`
  }

  // 表格列定义
  const columns: ColumnsType = [
    {
      title: '交易ID',
      dataIndex: 'transactionIndex',
      key: 'transactionIndex',
      width: 80,
      render: (index: number) => `#${index}`,
    },

    {
      title: '金额',
      key: 'amount',
      width: 120,
      render: (_, record: any) => {
        if (record.amount !== null) {
          return (
            <span style={{ fontWeight: 500 }}>
              {record.amount} {record.transferToken}
            </span>
          )
        }
        return <span style={{ color: '#999' }}>-</span>
      },
    },
    {
      title: 'From',
      dataIndex: 'from',
      key: 'from',
      width: 120,
      render: (address: string | null) => {
        if (address) {
          return (
            <span title={address} style={{ fontFamily: 'monospace', fontSize: '12px' }}>
              {formatAddress(address)}
            </span>
          )
        }
        return <span style={{ color: '#999' }}>-</span>
      },
    },
    {
      title: 'To',
      dataIndex: 'to',
      key: 'to',
      width: 120,
      render: (address: string | null) => {
        if (address) {
          return (
            <span title={address} style={{ fontFamily: 'monospace', fontSize: '12px' }}>
              {formatAddress(address)}
            </span>
          )
        }
        return <span style={{ color: '#999' }}>-</span>
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 140,
      render: (time: string | null) => formatTime(time),
    },

    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => {
        let color = 'default'
        let text = status

        // 对应 squads 平台的状态转换
        switch (status) {
          case 'Active':
            color = 'processing'
            text = 'Active'
            break
          case 'Approved':
            color = 'success'
            text = 'Ready'
            break
          case 'Executed':
            color = 'success'
            text = 'Executed'
            break
          case 'Cancelled':
            color = 'default'
            text = 'Cancelled'
            break
          case 'Rejected':
            color = 'error'
            text = 'Cancelled'
            break
        }

        return <Tag color={color}>{text}</Tag>
      },
    },
    {
      title: '投票进度',
      key: 'progress',
      width: 100,
      render: (_, record: any) => `${record.approvals}/${record.threshold}`,
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record: any) => {
        if (!publicKey || !currentMultisig) return null

        // 获取当前用户权限
        const currentUser = currentMultisig.members.find(member => member.address === publicKey.toBase58())
        if (!currentUser) return <span style={{ color: '#999' }}></span>

        const permissions = currentUser.permissions

        // Active 状态：显示 Approve 和 Reject 按钮
        if (record.status === 'Active') {
          if (!permissions.includes('Voter')) return <span style={{ color: '#999' }}></span>

          return (
            <Space>
              <Button type="primary" size="small" loading={actionLoading === `approve-${record.transactionIndex}`} onClick={() => handleVote(record.transactionIndex, 'approve')} icon={<CheckCircleOutlined />}>
                Approve
              </Button>
              <Button danger size="small" loading={actionLoading === `reject-${record.transactionIndex}`} onClick={() => handleVote(record.transactionIndex, 'reject')} icon={<CloseCircleOutlined />}>
                Reject
              </Button>
            </Space>
          )
        }

        // Ready 状态（Approved）：显示 Execute 和 Cancel 按钮
        if (record.status === 'Approved') {
          return (
            <Space>
              {permissions.includes('Executor') && (
                <Button type="primary" size="small" loading={actionLoading === `execute-${record.transactionIndex}`} onClick={() => handleExecute(record.transactionIndex)} icon={<PlayCircleOutlined />}>
                  Execute
                </Button>
              )}
              <Button size="small" onClick={() => handleCancel(record.transactionIndex)}>
                Cancel
              </Button>
            </Space>
          )
        }

        return null
      },
    },
  ]

  return (
    <>
      {contextHolder}
      <Card>
        <Table columns={columns} dataSource={transactions} loading={loading} rowKey="transactionIndex" pagination={false}/>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <span style={{ marginRight: 8 }}>每页显示：</span>
            <Select
              value={pagination?.pageSize || 5}
              onChange={(value) => handlePageSizeChange(pagination?.page || 1, value)}
              style={{ width: 80 }}
              options={[
                { label: '5条', value: 5 },
                { label: '10条', value: 10 },
                { label: '20条', value: 20 },
              ]}
            />
          </div>
          <div style={{ color: '#666', fontSize: '14px' }}>
            共 {pagination?.total || 0} 个交易，第 {pagination?.page || 1}/{pagination?.totalPages || 1} 页
          </div>
          {(pagination?.total || 0) > 0 && (
          <div style={{ marginTop: 16, textAlign: 'center' }}>
            <Pagination
              current={pagination?.page || 1}
              pageSize={pagination?.pageSize || 5}
              total={pagination?.total || 0}
              onChange={handlePageChange}
              onShowSizeChange={handlePageSizeChange}
              showSizeChanger={false} // 在上方已有选择器
              showQuickJumper
              showTotal={(total, range) =>
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
              }
            />
          </div>
        )}
        </div>
      </Card>
    </>
  )
}

export default TransactionsPage