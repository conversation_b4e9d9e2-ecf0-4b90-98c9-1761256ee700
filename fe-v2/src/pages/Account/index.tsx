import React, { useEffect, useState } from 'react';
import { Table, message } from 'antd';
import { useWallet } from '@solana/wallet-adapter-react';
import { useModel } from '@umijs/max';
import AddressCopy from '@/components/AddressCopy';

interface CoinAsset {
  symbol: string;
  address: string;
  balance: number;
  value: number;
}

const AccountPage: React.FC = () => {
  const { connected } = useWallet();
  const { multisigs, loading, refreshMultisigs } = useModel('multisig');
  const [assets, setAssets] = useState<CoinAsset[]>([]);

  useEffect(() => {
    refreshMultisigs()
  }, []);

  useEffect(() => {
    const buildAssets = () => {
      if (!connected || multisigs.length === 0) {
        setAssets([]);
        return;
      }

      try {
        const assets: CoinAsset[] = [];

        // 使用后端处理好的资产数据
        multisigs.forEach((multisig: any) => {
          if (multisig.vault.assets) {
            multisig.vault.assets.forEach((asset: any) => {
              assets.push({
                symbol: asset.symbol,
                address: asset.address,
                balance: asset.balance,
                value: asset.value,
              });
            });
          }
        });

        setAssets(assets);
      } catch (e) {
        message.error('获取资产失败');
      }
    };
    buildAssets();
  }, [connected, multisigs]);

  const columns = [
    {
      title: 'Coin',
      dataIndex: 'symbol',
      key: 'symbol',
      render: (_: any, record: CoinAsset) => (
        <div>{record.symbol}</div>
      )
    },
    {
      title: 'Address',
      dataIndex: 'address',
      key: 'address',
      render: (_: any, record: CoinAsset) => (
        <div>
          <AddressCopy address={record.address || ''} showFullAddress={true} />
        </div>
      )
    },
    {
      title: 'Balance',
      dataIndex: 'balance',
      key: 'balance',
      render: (v: number, record: CoinAsset) => (
        <span>{v.toFixed(5)} {record.symbol}</span>
      )
    },
    {
      title: 'Value (USD)',
      dataIndex: 'value',
      key: 'value',
      render: (v: number) => <span style={{ fontWeight: 700, color: '#52c41a' }}>${v.toFixed(2)}</span>
    }
  ];

  return (
    <div>
      <Table
        columns={columns}
        dataSource={assets}
        rowKey={r => r.symbol + r.address}
        loading={loading}
        pagination={false}
        bordered
      />
    </div>
  );
};

export default AccountPage;